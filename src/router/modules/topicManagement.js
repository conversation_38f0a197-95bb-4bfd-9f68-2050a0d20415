import layoutHeaderAside from '@/layout/header-aside'

// 由于懒加载页面太多的话会造成webpack热更新太慢，所以开发环境不使用懒加载，只有生产环境使用懒加载
const _import = require('@/libs/util.import.' + process.env.NODE_ENV)

const meta = { auth: true, cache: true }

export default [
  {
    path: '/topicManagement',
    name: 'topicManagement',
    redirect: { name: 'topicManage' },
    meta: {
      ...meta,
      title: '专题管理',
    },
    component: layoutHeaderAside,
    children: [
      {
        path: '',
        name: 'topicManage',
        meta: {
          ...meta,
          title: '专题管理',
        },
        component: _import('dcms/topicManage/topicManage.vue'),
      },
      {
        path: 'config',
        name: 'topicConfig',
        meta: {
          ...meta,
          title: '专题配置',
        },
        component: _import('dcms/topicManage/topicConfig.vue'),
      },
      {
        path: 'viewer',
        name: 'topicViewer',
        meta: {
          ...meta,
          title: '专题查看',
        },
        component: _import('dcms/topicManage/topicViewer.vue'),
      }
    ]
  }
]
