import layoutHeaderAside from '@/layout/header-aside'

// 由于懒加载页面太多的话会造成webpack热更新太慢，所以开发环境不使用懒加载，只有生产环境使用懒加载
const _import = require('@/libs/util.import.' + process.env.NODE_ENV)

const meta = { auth: true , cache: true}

export default [
  //报表管理
  {
    path: '/reportManagement',
    name: 'reportManagement',
    redirect: { name: 'reportManagement' },
    meta: {
      ...meta,
      title:'报表管理',
    },
    component: layoutHeaderAside,
    children:[
      {
        path: '',
        name: 'reportManagement',
        meta: {
          ...meta,
          title:'报表管理',
        },
        component: _import('dcms/report/reportManagement.vue'),
      },
      {
        path: 'fileQuaStatistics',
        name: 'fileQuaStatistics',
        meta: {
          ...meta,
          title: '文件数量统计'
        },
        component: _import('dcms/report/fileQuaStatisticsEnhanced.vue'),
      },
      {
        path: 'docDistriStatistics',
        name: 'docDistriStatistics',
        meta: {
          ...meta,
          title: '文件分发统计'
        },
        component: _import('dcms/report/docDistriStatisticsEnhanced.vue'),
      },
      {
        path: 'docReceiptStatistics',
        name: 'docReceiptStatistics',
        meta: {
          ...meta,
          title: '收函办理统计'
        },
        component: _import('dcms/report/docReceiptStatisticsEnhanced.vue'),
      },
      {
        path: 'invalidDocStatistics',
        name: 'invalidDocStatistics',
        meta: {
          ...meta,
          title: '作废文件统计'
        },
        component: _import('dcms/report/invalidDocStatisticsEnhanced.vue'),
      },
      {
        path: 'officeStatistics',
        name: 'officeStatistics',
        meta: {
          ...meta,
          title: '处室文件检查单-统计报表'
        },
        component: _import('dcms/report/officeStatistics.vue'),
      },
      {
        path: 'officeAbarbeitung',
        name: 'officeAbarbeitung',
        meta: {
          ...meta,
          title: '处室文件检查单-整改单报表'
        },
        component: _import('dcms/report/officeAbarbeitung.vue'),
      },
      {
        path: 'officeDetection',
        name: 'officeDetection',
        meta: {
          ...meta,
          title: '处室文件检查单-检测单报表'
        },
        component: _import('dcms/report/officeDetection.vue'),

      },
      {
        path: 'affectedDocTrackingReport',
        name: 'affectedDocTrackingReport',
        meta: {
          ...meta,
          title: '受影响文件跟踪单报表'
        },
        component: _import('dcms/report/affectedDocTrackingEnhanced.vue'),

      },
      {
        path: 'downloadReport',
        name: 'downloadReport',
        meta: {
          ...meta,
          title: '文件下载报表'
        },
        component: _import('dcms/report/downloadReport.vue'),

      },
      {
        path: 'unrepliedLetterStatistics',
        name: 'unrepliedLetterStatistics',
        meta: {
          ...meta,
          title: '发函未回复信函统计'
        },
        component: _import('dcms/report/unrepliedLetterStatisticsEnhanced.vue'),

      },
      {
        path: 'docReceiptTracking',
        name: 'docReceiptTracking',
        meta: {
          ...meta,
          title: '收函办理跟踪统计'
        },
        component: _import('dcms/report/docReceiptTrackingEnhanced.vue'),

      },
      {
        path: 'managerReport',
        name: 'managerReport',
        meta: {
          ...meta,
          title: '程序复审情况统计报表'
        },
        component: _import('dcms/report/managerReport.vue'),

      },
      {
        path: 'managerFilesReport',
        name: 'managerFilesReport',
        meta: {
          ...meta,
          title: '管理体系文件清单'
        },
        component: _import('dcms/report/managerFilesReport.vue'),

      },
      {
        path: 'managerFilesFromUsedReport',
        name: 'managerFilesFromUsedReport',
        meta: {
          ...meta,
          title: '管理体系文件阅览与表单使用情况'
        },
        component: _import('dcms/report/managerFilesFromUsedReport.vue'),

      },
      {
        path: 'managerCustomDocStatsReport',
        name: 'managerCustomDocStatsReport',
        meta: {
          ...meta,
          title: '管理体系文件的修编总体情况统计'
        },
        component: _import('dcms/report/managerCustomDocStatsReport.vue'),

      }
      ,
      {
        path: 'productDocReport',
        name: 'productDocReport',
        meta: {
          ...meta,
          title: '生产技术文件数据统计报表'
        },
        component: _import('dcms/report/productDocReport.vue'),

      },
      {
        path: 'productDocProcessReport',
        name: 'productDocProcessReport',
        meta: {
          ...meta,
          title: ' 生产技术文件各流程停留时长统计'
        },
        component: _import('dcms/report/productDocProcessReport.vue'),

      },
      {
        path: 'productDocSupportProcessReport',
        name: 'productDocSupportProcessReport',
        meta: {
          ...meta,
          title: '技术支持文件流程数据统计'
        },
        component: _import('dcms/report/productDocSupportProcessReport.vue'),

      },
      {
        path: 'workStationReport',
        name: 'workStationReport',
        meta: {
          ...meta,
          title: '工作站报表统计'
        },
        component: _import('dcms/report/workStationReport.vue'),

      },
      {
        path: 'docCheckReport',
        name: 'docCheckReport',
        meta: {
          ...meta,
          title: '文件接收检查单统计报表'
        },
        component: _import('dcms/report/docCheckReport.vue'),

      },
      {
        path: 'deptDocCheckReport',
        name: 'deptDocCheckReport',
        meta: {
          ...meta,
          title: '处室文件管理工作检查单报表'
        },
        component: _import('dcms/report/deptDocCheckReport.vue'),

      }

    ]
  },
]
