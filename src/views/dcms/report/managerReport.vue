
<!--程序复审情况统计-->
<template>
  <d2-container>
    <DataLayout>
      <template  style="height: auto">
         <el-form :inline="true">
          <el-form-item >
            <!-- 查询条件：程序编码（模糊匹配）、程序名称（模糊匹配）、程序管理人（模糊匹配）、责任处室（选择） -->
<!--            <QuerySelect v-model="createdDept"-->
<!--                :inputValue="createdDept" queryName="责任处室" placeholder="责任处室" ></QuerySelect>-->
            <QuerySelect v-model="createdDept"
                         :inputValue="createdDept" queryName="处室合集" placeholder="责任处室" ></QuerySelect>
          </el-form-item>
          <el-form-item >
            <el-input v-model="transferCoding" placeholder="程序编码"></el-input>
          </el-form-item>

          <el-form-item >
            <el-input v-model="title" placeholder="程序名称"></el-input>
          </el-form-item>
          <el-form-item >
              <el-input v-model="comment9" placeholder="程序管理人"></el-input>
            </el-form-item>
            <!--选择开始日期-->
            <!-- <el-form-item>
              <el-date-picker
                v-model="startDate"
                type="date"
                :placeholder="$t('application.startDate')"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item> -->
            <!--选择结束日期-->
            <!-- <el-form-item>
              <el-date-picker
                v-model="endDate"
                type="date"
                :placeholder="$t('application.endDate')"
                value-format="yyyy-MM-dd"
              ></el-date-picker>
            </el-form-item> -->

            <!--查询按钮-->
            <el-form-item>
              <el-button type="primary" plain @click="search()">{{
                  $t('application.SearchData')
                }}
              </el-button>
            </el-form-item>
            <!--导出查询结果按钮-->
            <el-form-item>
              <el-button type="primary" plain @click.native="exportExcel">{{
                  $t('application.ExportExcel')
                }}
              </el-button>
            </el-form-item>
        </el-form>
      </template>
      <!-- 列表字段：序号、程序编码、程序名称、程序管理人、责任处室、复审时间、复审情况 -->
        <!-- CODING, C_SERIAL_NUMBER,TITLE,C_CODE3,C_COMMENT9, C_ITEM2_DATE,C_TYPE2, C_APPROVER2,C_ITEM_STATUS1, STATUS, C_CREATED_DEPT -->
      <template v-slot:main="{ layout }">
        <el-main>
          <el-table v-loading="loading" :data="collectionChartData" id="reportTable" >
             <el-table-column
              type="index"
              width="50">
            </el-table-column>
            <el-table-column  width="180" label="编码" prop="CODING"></el-table-column>
            <el-table-column  width="100" label="程序编码" prop="C_SERIAL_NUMBER"></el-table-column>
            <el-table-column  width="250" label="标题" prop="TITLE"></el-table-column>
            <el-table-column width="150" label="状态" prop="STATUS"></el-table-column>
            <el-table-column width="150" label="程序状态" prop="C_ITEM_STATUS1"></el-table-column>
            <el-table-column width="150"  label="责任处室" prop="C_CREATED_DEPT"></el-table-column>
            <el-table-column width="150" label="程序名称" prop="C_CODE3"></el-table-column>
            <el-table-column width="150" label="程序管理人" prop="C_COMMENT9"></el-table-column>
            <el-table-column width="150" label="复审开始时间" prop="C_ITEM2_DATE"></el-table-column>
            <el-table-column width="150" label="复审结束时间" prop="C_ITEM2_DATE"></el-table-column>
            <el-table-column width="150"  label="复审情况" prop="C_CODE6"></el-table-column>

          </el-table>
          <!--分页-->
          <el-row class="report-page">
          <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[10, 20, 50, 100, 200]"
            :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total">
          </el-pagination>
          </el-row>
        </el-main>
      </template>
    </DataLayout>
  </d2-container>
</template>
<script>
import DataLayout from '@/components/ecm-data-layout'
import ExcelUtil from '@/utils/excel'
import QuerySelect from "@/components/controls/QuerySelect.vue";
export default {
  name: 'CancelProgram',
  data () {
    return {
      collectionChartData: [],//CODING, C_SERIAL_NUMBER,TITLE,C_CODE3,C_COMMENT9, C_ITEM2_DATE,C_TYPE2, C_APPROVER2,C_ITEM_STATUS1, STATUS, C_CREATED_DEPT
      coding:'',
      C_SERIAL_NUMBER:'',
      title:'',
      C_CODE3:'',
      C_COMMENT9:'',
      C_ITEM2_DATE:'',
      C_TYPE2:"",
      C_APPROVER2:'',//
      C_ITEM_STATUS1:'',//
      STATUS:'',//
      C_CREATED_DEPT:'',//
      C_CODE6:"",
      department:"",
      createdDept:'',//创建处室
      transferCoding:'',
      comment9: "",
      // startDate: '',            //开始日期
      // endDate: '',              //结束年份
      loading: false,
      findType: 'search',       //查询类型
      pageIndex: '',
      pageSize: 20,
      currentPage: 1,           //当前页数
      total:0,                  //总条数
    }
  },

  components: {
    DataLayout: DataLayout,
    QuerySelect:QuerySelect
  },

  methods: {
    // 分页 页数改变
    handleSizeChange (val) {
      this.pageSize = val
      this.search()
    },
    // 分页 当前页改变
    handleCurrentChange (val) {
      this.currentPage = val
      this.search()
    },
    //查询
    search () {
      let _self = this
      // if (_self.startDate == '' || _self.startDate == null) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择开始日期!',
      //     duration: 3000,
      //   })
      //   return
      // } else if (_self.endDate == '' || _self.endDate == null) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择结束日期!',
      //     duration: 3000,
      //   })
      //   return
      // }
      _self.loading = true

      let data = {
        url: '/managerreport/getProgramReviewtStat',
        isReqBody: true,
        title: _self.title,
        createdDept: _self.createdDept,
        comment9: _self.comment9,
        department: _self.department,
        transferCoding:_self.transferCoding,
        // treatStatus: _self.treatStatus,
        // classification1: _self.classification1,
        // startDate: _self.startDate,
        // endDate: _self.dateFormatAddDay(_self.endDate,1),
        findType: _self.findType,
        pageSize: _self.pageSize,
        pageIndex: _self.currentPage - 1,
        total:_self.total
      }
      _self.$restful(data)
        .then(function (response) {
          _self.collectionChartData = response.data.data;
          _self.$set(_self, 'total', response.data.total);

          _self.loading = false
          if (response.code != 200) {
            _self.$message(response.data.message)
            return
          }
          if (_self.findType == 'first') {
            _self.$message({
              showClose: true,
              message: '已查找近三个月数据',
              type: 'success',
              duration: 2000
            })
          } else if (_self.findType == 'search') {
            _self.$message({
              showClose: true,
              message: '已查找指定日期数据',
              type: 'success',
              duration: 2000
            })
          } else {
            _self.$message({
              showClose: true,
              message: '已查找所有数据',
              type: 'success',
              duration: 2000
            })
          }
        })
        .catch(function (error) {
          console.log(error)
        })
    },
    //导出查询结果
    exportExcel () {
      let _self = this
      // if (_self.startDate == '' || _self.startDate == null) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择开始日期!',
      //     duration: 3000,
      //   })
      //   return
      // } else if (_self.endDate == '' || _self.endDate == null) {
      //   this.$message({
      //     showClose: true,
      //     message: '请选择结束日期!',
      //     duration: 3000,
      //   })
      //   return
      // }
      let params = []
      let conds = {
        startDate: _self.startDate,
        endDate: _self.dateFormatAddDay(_self.endDate,1)
      }
      params = {
        URL: '/managerreport/ExportExcelManagerQuant',
        //condition: JSON.stringify(conds),
        isReqBody: true,
         title: _self.title,
        createdDept: _self.createdDept,
        transferCoding: _self.transferCoding,
        comment9: _self.comment9,
        findType: _self.findType,
        startDate: _self.startDate,
        endDate: _self.dateFormatAddDay(_self.endDate,1),
        lang: 'zh-cn',
      }
      ExcelUtil.export2Excel(params)
    },
  },
}
</script>
<style scoped>
.report-page {
  text-align: right;
  padding: 0 5px 0 0;
}
</style>
