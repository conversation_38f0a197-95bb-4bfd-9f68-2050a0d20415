<?xml version="1.0" encoding="UTF-8"?>
<svg width="1200" height="800" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 24px; font-weight: bold; fill: #2c3e50; }
      .module-title { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; font-weight: bold; fill: white; }
      .function-text { font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: white; }
      .workflow { fill: #3498db; stroke: #2980b9; stroke-width: 2; }
      .interface { fill: #e74c3c; stroke: #c0392b; stroke-width: 2; }
      .metadata { fill: #f39c12; stroke: #e67e22; stroke-width: 2; }
      .design { fill: #9b59b6; stroke: #8e44ad; stroke-width: 2; }
      .personal { fill: #1abc9c; stroke: #16a085; stroke-width: 2; }
      .admin { fill: #27ae60; stroke: #229954; stroke-width: 2; }
      .tools { fill: #34495e; stroke: #2c3e50; stroke-width: 2; }
    </style>
  </defs>
  
  <!-- 标题 -->
  <text x="600" y="30" text-anchor="middle" class="title">管理端系统功能架构图</text>
  
  <!-- 工作流管理模块 -->
  <rect x="50" y="80" width="200" height="150" rx="10" class="workflow"/>
  <text x="150" y="105" text-anchor="middle" class="module-title">工作流管理</text>
  <text x="70" y="130" class="function-text">• 流程配置</text>
  <text x="70" y="150" class="function-text">• Job管理</text>
  <text x="70" y="170" class="function-text">• 流程监控</text>
  <text x="70" y="190" class="function-text">• 流程设计</text>
  <text x="70" y="210" class="function-text">• 流程发布</text>
  
  <!-- 界面配置模块 -->
  <rect x="280" y="80" width="200" height="200" rx="10" class="interface"/>
  <text x="380" y="105" text-anchor="middle" class="module-title">界面配置</text>
  <text x="300" y="130" class="function-text">• 标签管理</text>
  <text x="300" y="150" class="function-text">• 组件管理</text>
  <text x="300" y="170" class="function-text">• 事件管理</text>
  <text x="300" y="190" class="function-text">• 菜单管理</text>
  <text x="300" y="210" class="function-text">• 表单管理</text>
  <text x="300" y="230" class="function-text">• 列表管理</text>
  <text x="300" y="250" class="function-text">• 查询管理</text>
  <text x="300" y="270" class="function-text">• 卡片查询管理</text>
  
  <!-- 元数据管理模块 -->
  <rect x="510" y="80" width="200" height="150" rx="10" class="metadata"/>
  <text x="610" y="105" text-anchor="middle" class="module-title">元数据管理</text>
  <text x="530" y="130" class="function-text">• 数据表管理</text>
  <text x="530" y="150" class="function-text">• 业务类型</text>
  <text x="530" y="170" class="function-text">• 存储管理</text>
  <text x="530" y="190" class="function-text">• 语言管理</text>
  <text x="530" y="210" class="function-text">• 数据字典</text>
  
  <!-- 后台管理模块 -->
  <rect x="50" y="320" width="500" height="400" rx="10" class="admin"/>
  <text x="300" y="345" text-anchor="middle" class="module-title">后台管理</text>
  
  <!-- 后台管理子功能 -->
  <text x="70" y="375" class="function-text">• 参数管理</text>
  <text x="70" y="395" class="function-text">• 用户管理</text>
  <text x="70" y="415" class="function-text">• 关系类型</text>
  <text x="70" y="435" class="function-text">• 文档分类</text>
  <text x="70" y="455" class="function-text">• ACL管理</text>
  <text x="70" y="475" class="function-text">• 缓存管理</text>
  
  <text x="300" y="375" class="function-text">• 文件夹管理</text>
  <text x="300" y="395" class="function-text">• 系统监控</text>
  <text x="300" y="415" class="function-text">• 日志管理</text>
  <text x="300" y="435" class="function-text">• 安全设置</text>
  <text x="300" y="455" class="function-text">• 存储管理</text>
  
  <!-- 管理工具子模块 -->
  <rect x="80" y="550" width="440" height="150" rx="5" class="tools"/>
  <text x="300" y="575" text-anchor="middle" class="module-title">其他管理工具</text>
  <text x="100" y="600" class="function-text">• 数据交换 - 数据交换文件生成</text>
  <text x="100" y="620" class="function-text">• 取号 - 手动取号</text>
  <text x="100" y="640" class="function-text">• 分发 - 文档手动分发管理</text>
  
  <!-- 系统特性说明 -->
  <rect x="580" y="320" width="490" height="400" rx="10" fill="#ecf0f1" stroke="#bdc3c7" stroke-width="2"/>
  <text x="825" y="345" text-anchor="middle" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 16px; font-weight: bold; fill: #2c3e50;">系统特性</text>
  
  <text x="600" y="380" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #2c3e50; font-weight: bold;">🔧 工作流引擎</text>
  <text x="620" y="400" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 支持复杂业务流程建模</text>
  <text x="620" y="420" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 任务自动分配与提醒</text>
  <text x="620" y="440" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 流程监控与统计分析</text>
  
  <text x="600" y="470" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #2c3e50; font-weight: bold;">📊 数据管理</text>
  <text x="620" y="490" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 灵活的元数据配置</text>
  <text x="620" y="510" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 多数据源支持</text>
  <text x="620" y="530" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 数据安全与权限控制</text>
  
  <text x="600" y="560" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #2c3e50; font-weight: bold;">🎨 界面定制</text>
  <text x="620" y="580" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 低代码配置</text>
  <text x="620" y="600" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 响应式布局支持</text>
  <text x="620" y="620" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 多语言切换</text>
  
  <text x="600" y="650" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 14px; fill: #2c3e50; font-weight: bold;">🔐 安全管控</text>
  <text x="620" y="670" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 细粒度权限控制</text>
  <text x="620" y="690" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 操作日志审计</text>
  <text x="620" y="710" style="font-family: 'Microsoft YaHei', Arial, sans-serif; font-size: 12px; fill: #34495e;">• 数据加密存储</text>
  
  <!-- 连接线 -->
  <line x1="150" y1="240" x2="300" y2="320" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="380" y1="290" x2="300" y2="320" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>
  <line x1="610" y1="240" x2="300" y2="320" stroke="#95a5a6" stroke-width="2" stroke-dasharray="5,5"/>

</svg>
