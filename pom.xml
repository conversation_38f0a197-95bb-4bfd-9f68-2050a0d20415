<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>2.7.2</version>
  </parent>

  <groupId>com.zisyun</groupId>
  <artifactId>zisecm</artifactId>
  <version>3.0.0</version>
  <packaging>pom</packaging>

  <properties>
    <zisecm.core.version>3.0.60</zisecm.core.version>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <java.version>1.8</java.version>
    <spring.version>2.7.2</spring.version>
    <aspectj.version>1.9.9.1</aspectj.version>
    <commons-lang3.version>3.12.0</commons-lang3.version>
    <boot.actuator.version>2.7.2</boot.actuator.version>
    <oracle.version>19.3.0.0</oracle.version>
  </properties>

  <!-- 发布maven私服 -->
  <distributionManagement>
    <snapshotRepository>
      <id>maven-snapshots</id>
      <url>http://10.16.7.12:8081/repository/maven-snapshots/</url>
    </snapshotRepository>
    <repository>
      <id>maven-releases</id>
      <url>http://10.16.7.12:8081/repository/maven-releases/</url>
    </repository>
  </distributionManagement>

  <!-- 依赖声明 -->
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>${spring.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
        <version>${boot.actuator.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>${spring.version}</version>
        <scope>test</scope>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <version>${spring.version}</version>
        <optional>true</optional>
      </dependency>

      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjweaver</artifactId>
        <version>${aspectj.version}</version>
      </dependency>

      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>

      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-actuator</artifactId>
        <version>${boot.actuator.version}</version>
      </dependency>

      <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>8.0.11</version>
      </dependency>

      <!-- 通用工具-->
      <dependency>
        <groupId>com.zisyun</groupId>
        <artifactId>zisecm-core</artifactId>
        <version>${zisecm.core.version}</version>
      </dependency>

      <dependency>
        <groupId>com.oracle.ojdbc</groupId>
        <artifactId>ojdbc8</artifactId>
        <version>${oracle.version}</version>
      </dependency>

      <!-- 由于报错改为1.2.18版本 com.alibaba:druid:jar:1.2.11 is invalid, transitive dependencies (if any) will not be available     -->
      <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>1.2.18</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok</artifactId>
      <version>1.18.12</version>
    </dependency>
  </dependencies>

  <modules>
  	<module>zisecm-portal</module>
    <module>zisecm-webservice</module>
  </modules>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.10.1</version>
        <configuration>
          <source>${java.version}</source>
          <target>${java.version}</target>
          <encoding>${project.build.sourceEncoding}</encoding>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
