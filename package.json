{"name": "DNMC", "version": "1.0.0", "license": "ISC", "author": "ATOS-PLM", "private": false, "scripts": {"serve": "vue-cli-service serve --open", "start": "npm run serve", "dev": "SET NODE_OPTIONS=--openssl-legacy-provider && npm run serve", "build": "vue-cli-service build", "build:preview": "NODE_OPTIONS=--max_old_space_size=4096 vue-cli-service build --mode preview", "lint": "vue-cli-service lint --fix", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"animate.css": "4.1.1", "axios": "^0.19.0", "axios-mock-adapter": "^1.18.1", "better-scroll": "^1.15.2", "core-js": "^3.4.3", "crypto-js": "^4.1.1", "dayjs": "^1.8.17", "echarts": "^5.4.0", "element-ui": "^2.13.1", "vue-echarts": "^6.5.4", "@antv/g2plot": "^2.4.0", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "file-saver": "^2.0.5", "flex.css": "^1.1.7", "hotkeys-js": "^3.7.3", "js-cookie": "^2.2.1", "lodash": "^4.17.15", "lowdb": "^1.0.0", "nprogress": "^0.2.0", "print-js": "^1.6.0", "qrcodejs2": "0.0.2", "screenfull": "^5.0.2", "sortablejs": "^1.10.1", "three": "^0.128.0", "three-obj-mtl-loader": "^1.0.3", "three-orbitcontrols": "^2.110.3", "ua-parser-js": "^0.7.20", "url-search-params-polyfill": "^8.1.1", "v-viewer": "^1.4.2", "video.js": "^7.11.8", "videojs-contrib-hls": "^5.15.0", "view-design": "^4.6.1", "vue": "^2.6.11", "vue-barcode": "^1.3.0", "vue-i18n": "^8.15.1", "vue-jsonp": "^2.0.0", "vue-photo-preview": "^1.1.3", "vue-print-nb": "^1.7.5", "vue-router": "^3.1.3", "vue-splitpane": "^1.0.6", "vuedraggable": "^2.23.2", "vuex": "^3.1.2", "wangeditor": "^4.7.8", "xlsx": "^0.17.0"}, "devDependencies": {"@d2-projects/vue-filename-injector": "^1.1.0", "@kazupon/vue-i18n-loader": "^0.5.0", "@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-unit-jest": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.1.0", "@vue/eslint-config-standard": "^5.1.2", "@vue/test-utils": "^1.0.2", "babel-eslint": "^10.0.3", "cache-loader": "^4.1.0", "compression-webpack-plugin": "^3.0.1", "cz-conventional-changelog": "^3.2.0", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^6.2.2", "sass": "^1.23.7", "sass-loader": "^8.0.0", "svg-sprite-loader": "^4.1.6", "text-loader": "^0.0.1", "vue-cli-plugin-i18n": "^1.0.1", "vue-template-compiler": "^2.6.10", "webpack-bundle-analyzer": "^3.6.0", "webpack-theme-color-replacer": "^1.3.3"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}