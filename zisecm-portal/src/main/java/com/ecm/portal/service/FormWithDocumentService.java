package com.ecm.portal.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ecm.common.util.CollectionUtil;
import com.ecm.common.util.FileUtils;
import com.ecm.common.util.JSONUtils;
import com.ecm.core.cache.manager.CacheManagerOper;
import com.ecm.core.dao.EcmCfgProcessMapper;
import com.ecm.core.entity.*;
import com.ecm.core.exception.EcmException;
import com.ecm.core.service.*;
import com.ecm.portal.log.MethodLogAnnotation;
import com.ecm.portal.util.DocFileUtils;
import com.ecm.portal.util.pdf.PDFUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
@Slf4j
@Service
public class FormWithDocumentService extends EcmService {
    @Autowired
    private RelationService relationService;
    @Autowired
    private FolderPathService folderPathService;
    @Autowired
    private ContentService contentService;
    @Autowired
    private FolderService folderService;
    @Autowired
    private DocumentService documentService;
    @Autowired
    private EcmCfgProcessMapper ecmCfgProcessMapper;

    public void addFiles(String token,Map<String,Object> metaData,MultipartFile mainFile,MultipartFile[] attachFiles)
            throws Exception{

        documentService.updateObject(token,metaData,false);
        addFiles(token,metaData.get("ID").toString(),mainFile,attachFiles);
    }


    @Transactional(rollbackFor = Exception.class)
    public void addFiles(String token,String id,MultipartFile mainFile,MultipartFile[] attachFiles)
            throws Exception {
        EcmDocument doc= documentService.getObjectById(token,id);
        if(doc==null){
            throw new EcmException("此ID："+id+"无对象！");
        }
        EcmContent en = null;
        if (mainFile != null) {

            en = new EcmContent();
            String[] paths=null;
//            if(File.separator.equals("\\")){
                paths= mainFile.getOriginalFilename().split("\\\\");
//            }else{
//                paths= mainFile.getOriginalFilename().split("/");
//            }
            if(paths.length>1){

                en.setName(paths[paths.length-1]);
            }else{
                en.setName(mainFile.getOriginalFilename());
            }
            EcmContent content= documentService.getContent(token,id);

            String fileName=en.getName();
            String[] names= fileName.split("_");

            if(doc.getAttributeValue("CLASSIFICATION_NAME")!=null){
                EcmClassification classification= CacheManagerOper.getEcmClassification(
                        doc.getAttributeValue("CLASSIFICATION_NAME").toString());
                if(classification==null){
                    throw new EcmException("分类“"+doc.getAttributeValue("CLASSIFICATION_NAME").toString()+"”不存在请联系管理员！");
                }
                if(doc.getAttributeValue("C_CLASSIFICATION1")!=null
                &&"程序".equals(doc.getAttributeValue("C_CLASSIFICATION1").toString())
                &&doc.getAttributeValue("CLASSIFICATION_NAME").toString().indexOf("表单")<0
                &&!"程序|程序新编申请".equals(doc.getAttributeValue("CLASSIFICATION_NAME").toString())
                &&(classification.getAddEfile()==2||classification.getAddEfile()==3)){
//                    if(org.springframework.util.StringUtils.isEmpty(doc.getCoding())){
//                        throw new EcmException("编码为空，请先填写或生成编码！");
//                    }
//                    if(!StringUtils.equals(doc.getCoding(),names[0].replaceAll("@","*"))){
//                        throw new EcmException("文件名称与编码不对应，格式：编码_版本.文件后缀");
//                    }
                }
            }

            en.setContentSize(mainFile.getSize());
            en.setFormatName(FileUtils.getExtention(mainFile.getOriginalFilename()));
            en.setInputStream(mainFile.getInputStream());
            if(content!=null && en.getFormatName().equals("docx")){
                content.setName(en.getName());
                content.setContentSize(en.getContentSize());
                content.setFormatName(en.getFormatName());
                content.setInputStream(en.getInputStream());
                documentService.creatOrUpdateObject(token, doc, content,false);
            }
            else if(content!=null){
                documentService.addRendition(token,id,en);
            }else{
                EcmContent existCont = contentService.getObject(token, id, 0, en.getFormatName());
                if(existCont!=null){
                    documentService.addRendition(token,id,en);
                }else{
                    documentService.creatOrUpdateObject(token, doc, en,false);
                }

            }

        }
        //创建附件
        if (attachFiles != null&&attachFiles.length>0) {
            if(doc.getTypeName().equals("程序表单库")){
                id=doc.getAttributeValue("C_DOC_ID").toString();
            }
            Map<String,Object> p=new HashMap<String, Object>();
            p.put("parentDocId", id);
            p.put("relationName", "irel_attachment");
            p.put("TYPE_NAME", "附件");
            execAddAttachment(token,p,attachFiles);
        }
    }

    @MethodLogAnnotation
    @Transactional(rollbackFor = Exception.class)
    public Map<String,Object> createFormWithDoc(String token,Map<String,Object> args, MultipartFile mainFile,
                                                MultipartFile[] attachFiles) throws Exception{
        boolean needValidatePolicy=args.get("needValidatePolicy")==null?
                true:Boolean.parseBoolean(args.get("needValidatePolicy").toString());
        EcmContent en = null;
        EcmDocument doc = new EcmDocument();
        if(args.get("ID")!=null){
            doc=documentService.getObjectById(token,args.get("ID").toString());
        }
        Object parentObj= args.get("parentId");
        Object templateId=args.get("templateId");
        Object relationObj=args.get("relationName");
        Object policy =args.get("policy")==null?args.get("POLICY"):args.get("policy");
        Object relations=args.get("relations");
        Object formInfo=args.get("formInfo");
        Object processConfigName=args.get("processConfigName");
        Object isform=args.get("isform")==null?args.get("ISFORM"):args.get("isform");
        Object copyToDoc = args.get("copyToDoc");
        //是否进行线下审批
        String offlineApproval=args.get("C_CODE9")==null?null:args.get("C_CODE9").toString();
        boolean hasEn=false;
        if(args.get("ID")!=null){
           EcmContent enc= contentService.getPrimaryContent(token,args.get("ID").toString());
           if(enc!=null&&enc.getContentSize()>0){
               hasEn=true;
           }else{
               hasEn=false;
           }
        }
        if(parentObj!=null) {
            args.remove("parentId");
        }
        if(relationObj!=null) {
            args.remove("relationName");
        }
        if(templateId!=null){
            args.remove("templateId");
        }
        if(policy!=null){
            args.remove("policy");
        }

        if(relations!=null){
            args.remove("relations");
        }
        if(isform!=null){
            args.remove("isform");
        }
        if(copyToDoc!=null){
            args.remove("copyToDoc");
        }

        Map<String,Object> formMap=new HashMap<>();
        if(formInfo!=null){
            args.remove("formInfo");
            formMap= JSONUtils.stringToMap(formInfo.toString());
        }
        if(processConfigName!=null){
            args.remove("processConfigName");
            formMap.put("processConfigName",processConfigName);
        }

        if (mainFile != null) {

            if(doc.getAttributeValue("CLASSIFICATION_NAME")!=null){
                EcmClassification cls= CacheManagerOper.getEcmClassification(doc.getAttributeValue("CLASSIFICATION_NAME").toString());
                String clsFormatName= cls.getFormatName();
                if(!StringUtils.isEmpty(clsFormatName)&&clsFormatName.indexOf(FileUtils.getExtention(mainFile.getOriginalFilename()))==-1){
                    throw new EcmException("分类“"+cls.getClassificationPath()+"”主文件格式为："+cls.getFormatName());
                }
            }

            en = new EcmContent();

            String[] paths=null;
//            if(File.separator.equals("\\")){
                paths= mainFile.getOriginalFilename().split("\\\\");
//            }else{
//                paths= mainFile.getOriginalFilename().split("/");
//            }
            if(paths.length>1){
                en.setName(paths[paths.length-1]);
            }else{
                en.setName(mainFile.getOriginalFilename());
            }
            String fileName=en.getName();
            String[] names= fileName.split("_");

            if(args.get("CLASSIFICATION_NAME")!=null){
                EcmClassification classification= CacheManagerOper.getEcmClassification(
                        args.get("CLASSIFICATION_NAME").toString());
                if(classification==null){
                    throw new EcmException("分类“"+doc.getAttributeValue("CLASSIFICATION_NAME").toString()+"”不存在请联系管理员！");
                }
                if("程序".equals(args.get("C_CLASSIFICATION1").toString())
                        &&args.get("CLASSIFICATION_NAME").toString().indexOf("表单")<0
                        &&!"程序|程序新编申请".equals(args.get("CLASSIFICATION_NAME").toString())
                        &&(classification.getAddEfile()==2||classification.getAddEfile()==3)){
                    if(org.springframework.util.StringUtils.isEmpty(args.get("CODING"))){
                        throw new EcmException("编码为空，请先填写或生成编码！");
                    }
                    if(!StringUtils.equals(args.get("CODING").toString(),names[0].replaceAll("@","*"))){
                        throw new EcmException("文件名称与编码不对应，格式：编码_版本.文件后缀");
                    }
                }
            }

//            en.setName(mainFile.getOriginalFilename());
            en.setContentSize(mainFile.getSize());
            en.setFormatName(FileUtils.getExtention(mainFile.getOriginalFilename()));
            en.setInputStream(mainFile.getInputStream());
//        }else if(!hasEn&&templateId!=null){
//            EcmContent tempEn= contentService.getPrimaryContent(token,templateId.toString());
//            String fullPath = CacheManagerOper.getEcmStores().get(tempEn.getStoreName()).getStorePath();
//            FileInputStream itemStream = new FileInputStream(fullPath+tempEn.getFilePath());
//            en = new EcmContent();
//            en.setName(tempEn.getName());
//            en.setContentSize(tempEn.getContentSize());
//            en.setFormatName(tempEn.getFormatName());
//            en.setInputStream(itemStream);
        }
        Object fid= args.get("folderId");
        String folderId="";
        if(fid==null) {
            if(args.get("ID")!=null){
               EcmDocument oldDoc= documentService.getObjectById(token,args.get("ID").toString());
               folderId=oldDoc.getFolderId();
            }
            if(folderId==null||"".equals(folderId)){
                //				folderId= folderPathService.getFolderId(token, doc.getAttributes(), "4");
                folderId=folderPathService.getFolderIdByPolicy(token,doc.getAttributes(),policy.toString());
            }

        }else {
            folderId=fid.toString();
            args.remove("folderId");
        }
        doc.setCurrent(false);
        doc.setAttributes(args);
        if(Boolean.parseBoolean(isform.toString())){
            doc.getAttributes().putAll(formMap);
//            doc.addAttribute("C_COMMENT3",formMap.get("C_COMMENT"));
//            doc.addAttribute("C_COMMENT1",formMap.get("C_COMMENT2"));
        }
//        if(copyToDoc != null){ // 属性设置，将form数据复制给doc（审、批人员）
//            if (Boolean.parseBoolean(copyToDoc.toString())) {
//                doc.getAttributes().putAll(formMap);
//            }
//        }
        if(doc.isRelease()){
            if(doc.getTypeName().equals("技术支持文件受影响文件")){
                if(parentObj==null){
                    doc.setStatus("新建");
                }else if(!parentObj.toString().startsWith("01c")){
                    doc.setStatus("新建");
                } else{
                    EcmFolder folder= folderService.getObjectById(token,parentObj.toString());
                    if(folder!=null){
                        doc.setStatus("新建");
                    }else{
                        doc.setStatus("设计中");
                    }

                }

            }else if(doc.getTypeName().contains("问题反馈")){
                //doc.setStatus("问题反馈已生效"); 为什么保存就改变状态呢？
                doc.setStatus("新建");
            }else {
                if(doc.getStatus()==null||"".equals(doc.getStatus())){
                    //如果设置了默认值不更改状态
                    doc.setStatus("已生效");
                    //新增了程序编号库，程序编码都来源于编号库，核应急预案没有走流程，所有需要在这里修改编码状态
                    String classification2=doc.getAttributes().get("C_CLASSIFICATION2")==null?"":doc.getAttributes().get("C_CLASSIFICATION2").toString();
                    if(classification2.equals("核应急预案")){
                        String docCoding=doc.getAttributes().get("CODING")==null?"":doc.getAttributes().get("CODING").toString();
                        String sql = "SELECT id FROM ECM_CUSTOM_FORM where CODING='"+docCoding+"' and TYPE_NAME='程序编号库'";
                        List<Map<String, Object>> fromObj=documentService.getMapList(token, sql);
                        String id=null;
                        for(int i=0;i<fromObj.size();i++){
                            id= fromObj.get(i).get("id").toString();
                            EcmDocument fromDoc= documentService.getObjectById(token,id);
                            Map<String, Object> map= new HashMap<>();
                            map.put("ID",id);
                            map.put("C_ITEM_STATUS1","已发布");
                            map.put("STATUS","已生效");
                            fromDoc.setAttributes(map);
                            documentService.updateObject(token,fromDoc);
                        }
                    }
                }

            }
        }else if(org.springframework.util.StringUtils.isEmpty(doc.getStatus())){
            doc.setStatus("新建");
        }

        EcmFolder folder= folderService.getObjectById(token, folderId);
        doc.setFolderId(folderId);
        if(doc.getAclName()==null||"".equals(doc.getAclName())){
            if(folder==null){
                doc.setAclName("acl_all_write");
            }else {
                doc.setAclName(folder.getAclName());
            }
        }

//			String id = documentService.newObject(token, doc, en);
        String id = documentService.creatOrUpdateObject(token, doc, en,needValidatePolicy);
        String formId="";
//        if(!"已生效".equals(doc.getStatus())&&!doc.getTypeName().equals("技术支持文件受影响文件"))
        if(!doc.isRelease()&&!doc.getTypeName().equals("技术支持文件受影响文件"))
        {
            String relationSql="select * from ECM_RELATION where NAME='irel_child' and CHILD_ID ='"+id+"'";
            List<Map<String, Object>> relationsList=  relationService.getMapList(token,relationSql);
            boolean hasRelation=false;
            if(relationsList!=null&&relationsList.size()>0){
                formMap.put("ID",relationsList.get(0).get("PARENT_ID"));
                hasRelation=true;
            }
            if(!Boolean.parseBoolean(isform.toString())){
                //创建form
                String formFolderId=folderPathService.getFolderIdByPolicy(token,doc.getAttributes(),"/草稿箱/表单");
                EcmFolder formFolder= folderService.getObjectById(token, formFolderId);
                formMap.put("FOLDER_ID",formFolderId);
                formMap.put("ACL_NAME",formFolder.getAclName());
                formMap.put("IS_RELEASED","0");
                if(offlineApproval != null && "是".equals(offlineApproval)){
                    formMap.put("C_CODE9",offlineApproval);
                }
                formId= createFormOrUpdate(token,formMap);
                //创建form与文件关联关系
                if(!StringUtils.isEmpty(formId)&&!hasRelation){
                    EcmRelation formRelation=new EcmRelation("irel_child",formId,id);
                    relationService.newObject(token, formRelation);
                }
            }

        }

        //创建附件
        if (attachFiles != null&&attachFiles.length>0) {

            Map<String,Object> p=new HashMap<String, Object>();
            p.put("parentDocId", id);
            p.put("relationName", "irel_attachment");
            p.put("TYPE_NAME", "附件");
            execAddAttachment(token,p,attachFiles);
        }
        //end
        if(parentObj!=null) {
            String relationName="irel_child";
            if(relationObj!=null) {
                relationName=relationObj.toString();
            }
            EcmRelation rel=new EcmRelation(relationName,parentObj.toString(),id);
            relationService.newObject(token, rel);
        }
        if(relations!=null){
            Map<String,Object> relationsMap= JSONUtils.stringToMap(relations.toString());
            Set<String> keySet= relationsMap.keySet();
            Iterator<String> keys= keySet.iterator();
            while(keys.hasNext()){
                String key=keys.next();
                EcmRelation rel=new EcmRelation(relationsMap.get(key).toString(),id,key);
                relationService.newObject(token, rel);
            }
        }
        Map<String,Object> result=new HashMap<>();
        if(Boolean.parseBoolean(isform.toString())){
            result.put("formId",id);
        }else{
            result.put("docId",id);
            result.put("formId",formId);
        }
        return result;
    }

    private List<JSONObject> getAllItem(JSONArray jsonArray, String type, List<JSONObject> list) {
        if (jsonArray == null || jsonArray.size() == 0) return null;
        String[] strings = {"node-key", "label", "isCurrent", "is_current","children"};
        List<String> excludeKey = Arrays.asList(strings);
        for (Object o : jsonArray) {
            JSONObject jsonObject = (JSONObject) o;
            String id = jsonObject.getString("id");
            // 审批保存的时候这里的key是parent_id
            String parentId = jsonObject.getString("parent_id");
            // 当ID和PARENT_ID值相同说明是顶级文章章节节点, 反之 说明不是章节节点
            if (StringUtils.isNotBlank(id) && !StringUtils.equals(id, parentId)) {
                //在不是顶级节点情况下，需要保留当前节点
                JSONObject tmp = new JSONObject();
                for (String s : jsonObject.keySet()) {
                    if (!excludeKey.contains(s)) {
                        tmp.put(s.toUpperCase(), jsonObject.getString(s));
                    }
                }
                list.add(tmp);
            }
            JSONArray children = jsonObject.containsKey("children") ? (JSONArray) jsonObject.get("children") : null;
            List<JSONObject> allItem = getAllItem(children, type, list);
            if (!CollectionUtil.isEmpty(allItem)) list.addAll(allItem);
        }
        return new ArrayList<>();
    }

    public void execAddAttachment(String token,Map<String, Object> args,MultipartFile[] uploadFile) throws Exception {
        String parentId = args.get("parentDocId").toString();
        boolean needValidatePolicy=args.get("needValidatePolicy")==null?
                true:Boolean.parseBoolean(args.get("needValidatePolicy").toString());
        for (MultipartFile multipartFile : uploadFile) {
            InputStream is = null;
            String tempFile = null;
            try {
                is = null;
                tempFile = null;
                Map<String, Object> tmp = new HashMap<>();
                tmp.putAll(args);
                EcmDocument doc = new EcmDocument();
                doc.setAttributes(tmp);

                String fName = multipartFile.getOriginalFilename();
                String fileName = "";
                String[] paths = null;
//            if(File.separator.equals("\\")){
                paths = fName.split("\\\\");
//            }else{
//                paths= fName.split("/");
//            }
                if (paths.length > 1) {

                    fileName = paths[paths.length - 1];
                } else {
                    fileName = fName;
                }
                String contentName = fileName;
                fileName = fileName.substring(0, fileName.lastIndexOf(".") < 0
                        ? fileName.length() : fileName.lastIndexOf("."));
                doc.setName(fileName);
                doc.setTitle(fileName);
                Object fid = args.get("folderId");
                String folderId = "";
                if (fid == null) {
                    folderId = folderPathService.getFolderId(token, doc.getAttributes(), "3");
                } else {
                    folderId = fid.toString();
                }
                EcmFolder folder = folderService.getObjectById(token, folderId);
                doc.setFolderId(folderId);
                doc.setAclName(folder.getAclName());

                EcmContent en = new EcmContent();
                en.setName(contentName);
                en.setContentSize(multipartFile.getSize());
               if(contentName!=null && contentName.toLowerCase().endsWith(".pdf")){
                   tempFile = CacheManagerOper.getEcmParameter("CacheFolder").getValue() + File.separator;
                   tempFile += UUID.randomUUID().toString()+".pdf";
                   DocFileUtils.writeStreamToFile(multipartFile.getInputStream(),tempFile);
                   if(CacheManagerOper.getEcmParameter("GetAttachmentCount")!=null && CacheManagerOper.getEcmParameter("GetAttachmentCount").getValue().equals("true")) {
                       int pageCount = PDFUtils.getPageCount(tempFile);
                       doc.addAttribute("C_PAGE_COUNT", pageCount);
                   }
                   is = DocFileUtils.getInputStream(tempFile);
                   en.setInputStream(is);
               }else {
                   doc.addAttribute("C_PAGE_COUNT",0);
                   en.setInputStream(multipartFile.getInputStream());
               }
//			documentService.addRendition(token, parentId, en);

                String relationName = "irel_child";
                relationName = args.get("relationName") != null
                        && !"".equals(args.get("relationName").toString())
                        ? args.get("relationName").toString() : "irel_child";
                String id = documentService.newObject(token, doc, en, needValidatePolicy);//创建文件和内容
                // doc docx 转PDF，用于浏览
//                if(doc.getFormatName()!=null && (doc.getFormatName().equals("doc") || doc.getFormatName().equals("docx"))){
//                    documentService.queue(token,id,"ecm_to_pdf","ecm_to_pdf",null);
//                }
                //----------------创建关系--------------
                EcmRelation relation = new EcmRelation();
                relation.setParentId(parentId);

                relation.setChildId(id);
                relation.setName(relationName);
                try {
                    relationService.newObject(token, relation);
                } catch (Exception e) {
                }
                if(CacheManagerOper.getEcmParameter("AttachmentToPDF")!=null && CacheManagerOper.getEcmParameter("AttachmentToPDF").getValue().equals("true")){
                    if(doc.getFormatName()!=null && (doc.getFormatName().equals("doc") || doc.getFormatName().equals("docx"))){
                        documentService.queue(token,id,"ecm_to_pdf","ecm_to_pdf",null);
                    }
                }
            }
            finally {
                if(is!=null){
                    try {
                        is.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if(tempFile!=null){
                    File tf = new File(tempFile);
                    tf.delete();
                }
                if (multipartFile != null && multipartFile.getInputStream()!=null) {
                    try {
                        multipartFile.getInputStream().close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            //----------------end创建关系-------------------
        }
    }
    /**
     *创建表单
     * @param token
     * @param formInfo
     * @return
     * @throws Exception
     */
    @MethodLogAnnotation
    private String createFormOrUpdate(String token,Map<String,Object> formInfo) throws Exception{
        Object processConfigObj= formInfo.get("processConfigName");
        if(processConfigObj==null){
            throw new EcmException("processConfigName不存在");
        }
        formInfo.remove("processConfigName");
        boolean needValidatePolicy=formInfo.get("needValidatePolicy")==null?
                true:Boolean.parseBoolean(formInfo.get("needValidatePolicy").toString());
        String processConfigName=processConfigObj.toString();
        List<EcmCfgProcess> ecmCfgProcess= ecmCfgProcessMapper.selectByPrimaryName(processConfigName);
        if(ecmCfgProcess==null||ecmCfgProcess.size()==0){
            throw new EcmException(processConfigName+"流程不存在");
        }
        String formType= ecmCfgProcess.get(0).getFormType();
        if(formType==null){
            throw new EcmException("请在“"+processConfigName+"”中配置表单类型（formType）");
        }
        formInfo.put("TYPE_NAME",formType);
        EcmDocument formObject=new EcmDocument();
        Map<String,Object> tmp=new HashMap<>();
        tmp.putAll(formInfo);
        log.info("formObject Attributes:{}", tmp);
        formObject.setAttributes(tmp);
        String id= documentService.creatOrUpdateObject(token,formObject,null,needValidatePolicy);
        return id;
    }
}
