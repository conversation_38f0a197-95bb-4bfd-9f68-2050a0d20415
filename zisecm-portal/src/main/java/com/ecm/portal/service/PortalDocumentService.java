package com.ecm.portal.service;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ecm.common.util.CollectionUtil;
import com.ecm.common.util.EcmStringUtils;
import com.ecm.core.AuditContext;
import com.ecm.core.cache.manager.CacheManagerOper;
import com.ecm.core.dao.EcmAclItemMapper;
import com.ecm.core.dao.EcmDocumentMapper;
import com.ecm.core.dao.EcmGroupUserMapper;
import com.ecm.core.db.DBFactory;
import com.ecm.core.entity.*;
import com.ecm.core.exception.AccessDeniedException;
import com.ecm.core.exception.EcmException;
import com.ecm.core.exception.NoPermissionException;
import com.ecm.core.service.*;
import com.ecm.core.util.AjaxResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import java.util.stream.Collectors;


@Slf4j
@Service
@Scope("prototype")
public class PortalDocumentService extends EcmObjectService<EcmDocument>{

	private static final Logger logger = LoggerFactory.getLogger(PortalDocumentService.class);

	private final String baseColumns = "ID,FOLDER_ID,CREATION_DATE, CREATOR, MODIFIER,OWNER_NAME,"
			+ "MODIFIED_DATE,REVISION,ACL_NAME,FORMAT_NAME,CONTENT_SIZE,ATTACHMENT_COUNT,"
			+ "IS_CURRENT,IS_HIDDEN,SYSTEM_VERSION,VERSION_ID,LOCK_OWNER,LOCK_DATE,LOCK_CLIENT,TYPE_NAME,LIFECYCLE_NAME,LIFECYCLE_STATUS,LIFECYCLE_DIR,STATUS";
	private final String filterColumns = ",ID,CREATION_DATE,CREATOR,MODIFIER,OWNER_NAME,"
			+ "MODIFIED_DATE,FORMAT_NAME,CONTENT_SIZE,"
			+ "SYSTEM_VERSION,VERSION_ID,LOCK_OWNER,LOCK_DATE,LOCK_CLIENT,";
	private final EcmDocumentMapper ecmDocument;
	@Autowired
	private FolderService folderService;
	@Autowired
	private ContentService contentServices;
	@Autowired
	private DocumentService documentService;
	@Autowired
	RelationService relationService;
	/**
	 * 权限数据访问
	 */
	@Autowired
	private EcmAclItemMapper ecmAclItem;
	@Autowired
	public PortalDocumentService(EcmDocumentMapper ecmDocumentMapper) {
		super(ecmDocumentMapper);
		this.ecmDocument=ecmDocumentMapper;
	}


	public AjaxResult simpleAccessContent(String typeList) {
		JSONObject jsonObject = new JSONObject();
		if (StringUtils.isNotBlank(typeList)) {
			JSONArray jsonArray = JSONArray.parseObject(typeList, JSONArray.class);
			jsonArray.forEach(m -> {
				JSONObject r = (JSONObject)m;
				String condition = null;
				if(r.containsKey("condition") && StringUtils.isNotBlank(r.getString("condition"))){
					condition = r.getString("condition");
				}
				//List<EcmDocument> ecmDocuments = ecmDocument.selectByTypeName(r.getString("title") , condition);
				String typeName = r.getString("title").replace("'", "");
				String tableName = CacheManagerOper.getEcmDefType(typeName).getDataTable();
				String sql = "Select ID,CREATION_DATE,TITLE,CODING,FORMAT_NAME from "+ tableName;
				sql += " where TYPE_NAME='" + typeName +"'";
				if(tableName.toLowerCase().equals("ecm_document")) {
					sql += " AND IS_RELEASED=1";
				}
				if(condition!=null) {
					sql += " AND " + condition;
				}
				sql += " ORDER BY CREATION_DATE DESC";
				sql = "select * from (" + sql +") where rownum<=5";
				jsonObject.put(r.getString("title"), ecmDocument.executeSQL(sql));
			});
		}
		return AjaxResult.success(jsonObject);
	}

	/**
	 * @param typeName 操作的type_name名称 如X光片
	 * @param process 操作：1.完成著录  2.完成确认  3.发布
	 * @param idList 以逗号拼接的id列表
	 * @return 操作成功/失败
	 */
	@Transactional(rollbackFor = Exception.class)
	public AjaxResult stateTransition(String token, String typeName, String process, String idList, String folderPath, String acl){
		if("X光片".equals(typeName)){
			if(StringUtils.isNotBlank(idList)){
				String[] split = idList.split(",");
				EcmDocument ecmDocument= new EcmDocument();
				List<EcmDocument> objectsByCondition = this.ecmDocument.getObjectsByCondition("ID in ('" + idList.replaceAll(",", "','") + "')");
				if("1".equals(process)){ //1.完成著录  完成著录：修改状态STATUS为“待确认”
					List<EcmDocument> collect = objectsByCondition.stream().filter(r -> "新建".equals(r.getStatus())).collect(Collectors.toList());
					if(collect.isEmpty()){
						return AjaxResult.error("application.pleaseSelectRightObj");
					}
					this.ecmDocument.batchUpdateStatusByPrimaryKey("待确认", null, null, collect, null, null);
				}
				else if("2".equals(process)){ //2.完成确认  	完成确认：修改状态STATUS为“已确认”
					List<EcmDocument> collect = objectsByCondition.stream().filter(r -> "新建".equals(r.getStatus()) || "待确认".equals(r.getStatus())).collect(Collectors.toList());
					if(collect.isEmpty()){
						return AjaxResult.error("application.pleaseSelectRightObj");
					}
					this.ecmDocument.batchUpdateStatusByPrimaryKey("已确认", null,null, collect, null, null);
				}
				else if("3".equals(process)){// 3.发布  发布：修改状态STATUS为“已入库”
					List<EcmDocument> collect = objectsByCondition.stream().filter(r -> !"已入库".equals(r.getStatus())).collect(Collectors.toList());
					if(collect.isEmpty()){
						return AjaxResult.error("application.pleaseSelectRightObj");
					}
					// 根据文件夹路径获取文件夹ID
					String folderId = null;
					EcmFolder folder = folderService.getObjectByPath(token, folderPath);
					if(folder != null) {
						folderId = folder.getId();
					}
					// 根据密集设置ACL
					Map<String, List<EcmDocument>> docMap = new HashMap<>();
					for (EcmDocument document : collect) {
						if ("内部公开".equals(document.getSecurityLevel())) {
							if(StringUtils.isEmpty(acl)){
								acl = "acl_release_scuret";
							}
						} else {
							acl = "acl_release_scuret";
						}
						List<EcmDocument> tmpList = docMap.get(acl);
						if (CollectionUtil.isEmpty(tmpList)) {
							tmpList = new ArrayList<>();
						}
						tmpList.add(document);
						docMap.put(acl, tmpList);
					}
					for (String aclName : docMap.keySet()) {
						if (!CollectionUtil.isEmpty(docMap.get(aclName))) {
							this.ecmDocument.batchUpdateStatusByPrimaryKey("已入库", 1, new Date(),  docMap.get(aclName), folderId, aclName);
						}
					}

//
//					1、	根据密级设置权限
//					内部公开：根据分类查找分类配置，如果设置了发布ACL使用发布ACL名称，否则使用通用ACL：acl_release_public
//					其他：暂时使用acl_release_scuret，后续需要修改成商密事项相关。

//					2、	设置发布目录
//					根据分类名称查找分类配置，根据分类配置的路径设置FOLDER_ID


//					3、	设置发布属性
//					名称	属性	值
//					状态	STATUS	已生效
//					发布标识	IS_RELEASED	1
//					版本日期	C_REVISION_DATE	当期日期
				}else{
					log.error("Unable to obtain specific operation business");
				}
			}
			else{
				log.warn("Unable to get the list of IDS for the operation. The business operation will be ignored");
			}
		}
		else{
			log.warn("Business logic is empty, status modification will be ignored");
		}
		return AjaxResult.success();
	}




	/**
	 * 相关区域相关设备的复制
	 * @param token
	 * @param oldId
	 * @param newId
	 * @return
	 * @throws Exception
	 */
	public boolean copyZoneAndEquipment(String token,String oldId,String newId) throws Exception {
		//String queryZone="select * from KM_ZONE where C_DOC_ID='"+oldId+"'";
		String queryEquip="select * from KM_EQUIPMENTE where C_DOC_ID='"+oldId+"'";
		//List<Map<String,Object>> zoneList= getMapList(token,queryZone);
		List<Map<String,Object>> equipList= getMapList(token,queryEquip);
//		for (int i = 0; zoneList!=null&&i < zoneList.size(); i++) {
//			Map<String,Object> zone=zoneList.get(i);
//			zone.put("C_DOC_ID",newId);
//			documentService.saveAsNew(token,zone.get("ID").toString(),zone,false);
//		}

		for (int i = 0; equipList!=null&&i < equipList.size(); i++) {
			Map<String,Object> equip=equipList.get(i);
			equip.put("C_DOC_ID",newId);
			documentService.saveAsNew(token,equip.get("ID").toString(),equip,false);
		}
		return true;
	}

	/*
	 * 根据父ID迭代删除ID
	 * <AUTHOR>
	 * @param parentId
	 */
	public int deleteByParentId(String parentId){
		return this.ecmDocument.recursionDeleteByParentId(parentId);
	}




	/**
	 * 将有父子关系的数据转换成树形结构数据
	 * @return 最终的树状结构的集合数据
	 */
	private List<JSONObject> getTree(String parentId) {
		// 获取数据库中带有有父子关系的数据
		List<JSONObject> data = this.ecmDocument.recursionGetAllByParentId(parentId);
		//创建一个List集合来存放最终的树状结构数据
		List<JSONObject> treeList = new ArrayList<>();
		for (JSONObject r : data) {
			Map<String, Object> map = new HashMap<>();
			if (parentId.equals(r.get("PARENT_ID"))) {
				JSONObject temp = new JSONObject();
				r.keySet().forEach(j -> {
					temp.put(j.toLowerCase(), StringUtils.isNotBlank(r.getString(j)) && !StringUtils.equalsIgnoreCase(r.getString(j),"null") ? r.getString(j) : "");
				});
				String name =  StringUtils.isNotBlank(r.getString("NAME")) && !StringUtils.equalsIgnoreCase(r.getString("NAME"),"null") ? r.getString("NAME") : "";
				name = StringUtils.isNotBlank(name) ? name.concat("-") : "";
				temp.put("label", name + r.getString("TITLE"));
				temp.put("node-key", r.get("ID"));
				temp.put("children", getChildren(data, r.getString("ID")));
				treeList.add(temp);
			}
		}
		return treeList;
	}

	/**
	 * 递归处理：通过parentId获取子级，查询子级下的子级
	 *
	 * @param data 数据库的原始数据
	 * @param id   主id
	 * @return 该id下得子级
	 */
	private List<JSONObject> getChildren(List<JSONObject> data, String id) {
		List<JSONObject> list = new ArrayList<>();
		if (data == null || data.size() == 0 || StringUtils.isBlank(id)) {
			return list;
		}
		for (JSONObject r : data) {
			Map<String, Object> map = new HashMap<>();
			if (id.equals(r.get("PARENT_ID"))) {
				JSONObject temp = new JSONObject();
				r.keySet().forEach(j -> {
					temp.put(j.toLowerCase(), StringUtils.isNotBlank(r.getString(j)) ? r.getString(j) : "");
				});
				temp.put("label", r.getString("NAME") + "-" + r.getString("TITLE"));
				temp.put("node-key", r.get("ID"));
				temp.put("children", getChildren(data, r.getString("ID")));
				list.add(temp);
			}
		}
		return list;
	}



	/***
	 * 初始化章节方法
	 * @param list
	 * @param pDocId
	 * @return
	 */
	public AjaxResult initChapter(List<HashMap<String, Object>> list, String pDocId) {
		List<Object> listReturn = new ArrayList<>();
		for (int i = 1; i <= list.size(); i++) {
			HashMap<String, Object> jsonObject = list.get(i - 1);
			String id = String.valueOf(jsonObject.get("ID") == null ? "" : jsonObject.get("ID"));
			String selSql = "SELECT id as \"id\",SUB_TYPE as \"sub_type\",PARENT_ID as \"parent_id\",NAME as \"name\",IS_CURRENT  as \"is_current\",title as \"title\",C_NPP_CODE as \"c_npp_code\",REVISION  as \"revision\",C_SOURCE_CODE as \"c_source_code\",C_COMMENT as \"c_comment\",C_CODE1  as \"c_code1\",C_ORDER_INDEX as \"c_order_index\",C_COMMENT1 as \"comment1\",C_EDIT_STATUS  as \"c_edit_status\" FROM ECM_DOCUMENT WHERE ID='";
			List<Map<String, Object>> maps = this.ecmDocument.executeSQL( selSql + id + "'");
			Map<String, Object> map = maps.size() > 0 ? maps.get(0) : null;
			JSONObject jsonRtn = new JSONObject();
			String title = String.valueOf(jsonObject.get("chapter_title"));
			if (map != null) {
				map.put("parent_id", pDocId);
				map.put("sub_type", "1");
				map.put("c_edit_status", "N");
				map.put("name", ""); // 章节号
				map.put("title", title); //章节名称
				map.put("type_name", "程序章节");
				map.put("label", (null != map.get("name") && !"".equals(map.get("name")) ? String.valueOf(map.get("name")) + "-" : "") + (null != map.get("title") ? String.valueOf(map.get("title")) : ""));
//				map.put("acl_name","acl_all_write"); //章节acl_name默认为acl_all_write
				map.put("c_order_index", String.valueOf(i));
				if (StringUtils.isNotBlank(id)) {
					String sql = "UPDATE ECM_DOCUMENT SET C_ORDER_INDEX='%s', PARENT_ID='%s', SUB_TYPE='1', TITLE='%s', TYPE_NAME='%s', ACL_NAME='acl_all_write', C_EDIT_STATUS='%s' where ID='%s'";
					sql = String.format(sql, String.valueOf(i), pDocId, title, "程序章节", "N", id);
					this.ecmDocument.executeSQL(sql);

					listReturn.add(map);
				}

			}
		}
		return AjaxResult.success(listReturn);
	}



	/**
	 * 通过条件获取文件指定字段值
	 * @param fields 字段
	 * @param conditions 条件
	 * @return
	 */
	public AjaxResult getDocFieldValByCondition (JSONArray fields, JSONObject conditions) {
		// 搜索字段处理
		if (fields.isEmpty()) {
			return AjaxResult.error();
		}
		String fieldStr = "";
		for (Object field : fields) {
			String fieldStrTmp = (String) field;
			fieldStrTmp = DBFactory.getDBConn().getDBUtils().getString(fieldStrTmp);
			if (com.alibaba.druid.util.StringUtils.isEmpty(fieldStrTmp)) {
				continue;
			}
			if (com.alibaba.druid.util.StringUtils.isEmpty(fieldStr)) {
				fieldStr += fieldStrTmp;
			} else {
				fieldStr += "," +fieldStrTmp;
			}
		}
		if (com.alibaba.druid.util.StringUtils.isEmpty(fieldStr)) {
			return AjaxResult.error();
		}

		// 搜索条件处理
		String conditionsStr = "";
		// 子条件处理
		String childCondStr = "";
		JSONObject childConditions = (JSONObject) conditions.get("child");
		if (childConditions != null) {
			for (String key : childConditions.keySet()) {
				String val = (String) childConditions.get(key);
				if (com.alibaba.druid.util.StringUtils.isEmpty(key) || com.alibaba.druid.util.StringUtils.isEmpty(val)) {
					continue;
				}
				if ("NULL".equals(val)) {
					val = " is null ";
				} else {
					val = " = '" + DBFactory.getDBConn().getDBUtils().getString(val) + "' ";
				}
				if (!com.alibaba.druid.util.StringUtils.isEmpty(childCondStr)) {
					childCondStr += " and ";
				}
				childCondStr += DBFactory.getDBConn().getDBUtils().getString(key) + val;
			}
		}
		// 关联关系条件处理
		String relCondStr = "";
		JSONObject relationConditions = (JSONObject) conditions.get("relation");
		if (relationConditions != null) {
			for (String key : relationConditions.keySet()) {
				String val = (String) relationConditions.get(key);
				if (com.alibaba.druid.util.StringUtils.isEmpty(key) || com.alibaba.druid.util.StringUtils.isEmpty(val)) {
					continue;
				}
				if ("NULL".equals(val)) {
					val = " is null ";
				} else {
					val = " = '" + DBFactory.getDBConn().getDBUtils().getString(val) + "' ";
				}
				if (!com.alibaba.druid.util.StringUtils.isEmpty(relCondStr)) {
					relCondStr += " and ";
				}
				relCondStr += DBFactory.getDBConn().getDBUtils().getString(key) + val;
			}
		}
		// 父条件处理
		String parCondStr = "";
		JSONObject parentConditions = (JSONObject) conditions.get("parent");
		if (parentConditions != null) {
			for (String key : parentConditions.keySet()) {
				String val = (String) parentConditions.get(key);
				if (com.alibaba.druid.util.StringUtils.isEmpty(key) || com.alibaba.druid.util.StringUtils.isEmpty(val)) {
					continue;
				}
				if ("NULL".equals(val)) {
					val = " is null ";
				} else {
					val = " = '" + DBFactory.getDBConn().getDBUtils().getString(val) + "' ";
				}
				if (!com.alibaba.druid.util.StringUtils.isEmpty(parCondStr)) {
					parCondStr += " and ";
				}
				parCondStr += DBFactory.getDBConn().getDBUtils().getString(key) + val;
			}
		}

		if (com.alibaba.druid.util.StringUtils.isEmpty(childCondStr) && com.alibaba.druid.util.StringUtils.isEmpty(relCondStr)){ // 父条件仅在关联关系条件存在时判断
			return AjaxResult.error();
		}

		String sql = "select " + fieldStr + " from ECM_DOCUMENT where 1=1 ";
		if (!com.alibaba.druid.util.StringUtils.isEmpty(childCondStr)) {
			sql += " and " + childCondStr;
		}

		if (!com.alibaba.druid.util.StringUtils.isEmpty(relCondStr)) {
			if (!com.alibaba.druid.util.StringUtils.isEmpty(parCondStr)) {
				sql += " and ID in ( select CHILD_ID from ECM_REALTION where " + relCondStr
						+ " and PARENT_ID in ( select ID from ECM_DOCUMENT where " + parCondStr + " )) ";
			} else {
				sql += " and ID in ( select CHILD_ID from ECM_REALTION where " + relCondStr + " ) ";
			}
		}

		return AjaxResult.success(ecmDocument.executeSQL(sql));
	}



	private void insertIntoChapter(int order_index, JSONObject r){
		String insertSql = "INSERT INTO ECM_DOCUMENT (%s) VALUES (%s)";
		String fields = "", values = "";
		for (String s : r.keySet()) {
			fields += StringUtils.isNotBlank(fields) ? ",".concat(s) : s;
			String str = "";
			if (StringUtils.equalsIgnoreCase("c_order_index", s)) {
				str = str.concat("'" + order_index + "'");
			} else {
				str = "'" + DBFactory.getDBConn().getDBUtils().getString(StringUtils.isNotBlank(r.getString(s)) ? r.getString(s) : "") + "'";
			}
			values += StringUtils.isNotBlank(values) ? ",".concat(str) : str;
		}
		insertSql = String.format(insertSql, fields, values);
		this.ecmDocument.executeSQL(insertSql);
	}


	public void techFileExamineInitRel(String token,String newObjId,String examineType)  {
		String examineItem = "";
		String sql = "";
		try {
			if(examineType.equals("维修规程")){
				sql = "SELECT ID ,NAME FROM ECM_CUSTOM_CONFIG where SUB_TYPE='生产技术文件维修规程审查项'  AND TYPE_name='配置项' AND STATUS='启用'  ORDER BY C_ORDER_INDEX ";
				List<Map<String,Object>> list = documentService.getMapList(token,sql);
				for ( Map<String,Object> mp: list) {
					try {
						examineItem = mp.get("NAME").toString();
						EcmDocument docItem = new EcmDocument();
						docItem.addAttribute("C_COMMENT",examineItem); //检查项描述
						docItem.addAttribute("C_STRING1", "");//意见
						docItem.addAttribute("C_COMMENT1", ""); //需修订内容
						docItem.addAttribute("TYPE_NAME","维修规程审查项");
					    String examineId = 	documentService.creatOrUpdateObject(token, docItem, null,true);

						EcmRelation rel = new EcmRelation();
						rel.createId();
						rel.setName("维修规程审查项");
						rel.setParentId(newObjId);
						rel.setChildId(examineId);
						relationService.newObject(token, rel);
					} catch (Exception ex) {
						ex.printStackTrace();
					}
				}
			}
			if(examineType.equals("运行规程")){
				sql = "SELECT ID ,NAME FROM ECM_CUSTOM_CONFIG where SUB_TYPE='生产技术文件运行规程审查项'  AND TYPE_name='配置项' AND STATUS='启用'  ORDER BY C_ORDER_INDEX ";
				List<Map<String,Object>> list = documentService.getMapList(token,sql);
				for ( Map<String,Object> mp: list) {
					try {
						examineItem = mp.get("NAME").toString();
						EcmDocument docItem = new EcmDocument();
						docItem.addAttribute("C_COMMENT",examineItem); //审查内容
						docItem.addAttribute("C_STRING1", ""); //是否有意见
						docItem.addAttribute("C_COMMENT2", ""); //附件/步骤及问题描述
						docItem.addAttribute("C_COMMENT3", ""); //修改依据
						docItem.addAttribute("C_COMMENT1", ""); //修改建议
						docItem.addAttribute("TYPE_NAME","运行规程审查项");
						String examineId = 	documentService.creatOrUpdateObject(token, docItem, null,true);

						EcmRelation rel = new EcmRelation();
						rel.createId();
						rel.setName("运行规程审查项");
						rel.setParentId(newObjId);
						rel.setChildId(examineId);
						relationService.newObject(token, rel);
					} catch (Exception ex) {
						ex.printStackTrace();
					}
				}

			}
//			if(examineType.equals("其他生产技术文件")){ //不需要自动创建
//				sql = "SELECT ID ,NAME FROM ECM_CUSTOM_CONFIG where SUB_TYPE='生产技术文件文件审查项'  AND TYPE_name='配置项' AND STATUS='启用'  ORDER BY C_ORDER_INDEX ";
//				List<Map<String,Object>> list = documentService.getMapList(token,sql);
//				for ( Map<String,Object> mp: list) {
//					try {
//						examineItem = mp.get("NAME").toString();
//						EcmDocument docItem = new EcmDocument();
//						docItem.addAttribute("C_COMMENT",examineItem); //检查项描述
//						docItem.addAttribute("C_STRING1", "");//意见
//						docItem.addAttribute("C_COMMENT1", ""); //需修订内容
//						docItem.addAttribute("TYPE_NAME","文件审查项");
//						String examineId = 	documentService.creatOrUpdateObject(token, docItem, null,true);
//
//						EcmRelation rel = new EcmRelation();
//						rel.createId();
//						rel.setName("文件审查项");
//						rel.setParentId(newObjId);
//						rel.setChildId(examineId);
//						relationService.newObject(token, rel);
//					} catch (Exception ex) {
//						ex.printStackTrace();
//					}
//				}
//			}
		} catch (EcmException e) {
			e.printStackTrace();
		}

	}


}
