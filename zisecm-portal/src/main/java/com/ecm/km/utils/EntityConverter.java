package com.ecm.km.utils;

import java.util.Map;
import java.util.List;
import java.util.ArrayList;

/**
 * 实体对象转换工具类
 * 用于将Map转换为实体对象
 */
public class EntityConverter {
    
    /**
     * 将Map转换为DocumentTopic对象
     * @param map 包含数据的Map
     * @return DocumentTopic对象
     */
    public static com.ecm.km.entity.DocumentTopic mapToDocumentTopic(Map<String, Object> map) {
        if (map == null) {
            return null;
        }
        
        com.ecm.km.entity.DocumentTopic documentTopic = new com.ecm.km.entity.DocumentTopic();
        
        if (map.get("id") != null) documentTopic.setId(map.get("id").toString());
        if (map.get("topicCategoryId") != null) documentTopic.setTopicCategoryId(map.get("topicCategoryId").toString());
        if (map.get("topicCategoryName") != null) documentTopic.setTopicCategoryName(map.get("topicCategoryName").toString());
        if (map.get("documentCode") != null) documentTopic.setDocumentCode(map.get("documentCode").toString());
        if (map.get("documentTitle") != null) documentTopic.setDocumentTitle(map.get("documentTitle").toString());
        if (map.get("version") != null) documentTopic.setVersion(map.get("version").toString());
        if (map.get("useStatus") != null) {
            try {
                documentTopic.setUseStatus(Integer.parseInt(map.get("useStatus").toString()));
            } catch (NumberFormatException e) {
                documentTopic.setUseStatus(1); // 默认值
            }
        }
        if (map.get("documentStatus") != null) documentTopic.setDocumentStatus(map.get("documentStatus").toString());
        if (map.get("categoryName") != null) documentTopic.setCategoryName(map.get("categoryName").toString());
        if (map.get("topicIdentifier") != null) documentTopic.setTopicIdentifier(map.get("topicIdentifier").toString());
        if (map.get("sortOrder") != null) {
            try {
                documentTopic.setSortOrder(Integer.parseInt(map.get("sortOrder").toString()));
            } catch (NumberFormatException e) {
                documentTopic.setSortOrder(0); // 默认值
            }
        }
        if (map.get("remark") != null) documentTopic.setRemark(map.get("remark").toString());
        
        return documentTopic;
    }
    
    /**
     * 将Map转换为TopicCategory对象
     * @param map 包含数据的Map
     * @return TopicCategory对象
     */
    public static com.ecm.km.entity.TopicCategory mapToTopicCategory(Map<String, Object> map) {
        if (map == null) {
            return null;
        }
        
        com.ecm.km.entity.TopicCategory topicCategory = new com.ecm.km.entity.TopicCategory();
        
        if (map.get("id") != null) topicCategory.setId(map.get("id").toString());
        if (map.get("categoryCode") != null) topicCategory.setCategoryCode(map.get("categoryCode").toString());
        if (map.get("categoryName") != null) topicCategory.setCategoryName(map.get("categoryName").toString());
        if (map.get("description") != null) topicCategory.setDescription(map.get("description").toString());
        if (map.get("parentId") != null) topicCategory.setParentId(map.get("parentId").toString());
        if (map.get("level") != null) {
            try {
                topicCategory.setLevel(Integer.parseInt(map.get("level").toString()));
            } catch (NumberFormatException e) {
                topicCategory.setLevel(1); // 默认值
            }
        }
        if (map.get("sortOrder") != null) {
            try {
                topicCategory.setSortOrder(Integer.parseInt(map.get("sortOrder").toString()));
            } catch (NumberFormatException e) {
                topicCategory.setSortOrder(0); // 默认值
            }
        }
        if (map.get("status") != null) {
            try {
                topicCategory.setStatus(Integer.parseInt(map.get("status").toString()));
            } catch (NumberFormatException e) {
                topicCategory.setStatus(1); // 默认值
            }
        }
        if (map.get("isLeaf") != null) {
            try {
                topicCategory.setIsLeaf(Integer.parseInt(map.get("isLeaf").toString()));
            } catch (NumberFormatException e) {
                topicCategory.setIsLeaf(1); // 默认值
            }
        }
        
        return topicCategory;
    }
    
    /**
     * 安全的字符串转整数方法
     * @param value 字符串值
     * @param defaultValue 默认值
     * @return 转换后的整数
     */
    public static Integer safeParseInt(String value, Integer defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return Integer.parseInt(value.trim());
        } catch (NumberFormatException e) {
            return defaultValue;
        }
    }
    
    /**
     * 安全的字符串转换方法
     * @param value 对象值
     * @return 转换后的字符串，如果为null则返回空字符串
     */
    public static String safeToString(Object value) {
        return value != null ? value.toString() : "";
    }
    
    /**
     * 将Map列表转换为DocumentTopic对象列表
     * @param mapList Map对象列表
     * @return DocumentTopic对象列表
     */
    @SuppressWarnings("unchecked")
    public static List<com.ecm.km.entity.DocumentTopic> mapListToDocumentTopicList(Object mapList) {
        if (mapList == null) {
            return new ArrayList<>();
        }
        
        List<com.ecm.km.entity.DocumentTopic> result = new ArrayList<>();
        
        if (mapList instanceof List) {
            List<?> list = (List<?>) mapList;
            for (Object item : list) {
                if (item instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) item;
                    com.ecm.km.entity.DocumentTopic documentTopic = mapToDocumentTopic(map);
                    if (documentTopic != null) {
                        result.add(documentTopic);
                    }
                }
            }
        }
        
        return result;
    }
    
    /**
     * 将Map列表转换为TopicCategory对象列表
     * @param mapList Map对象列表
     * @return TopicCategory对象列表
     */
    @SuppressWarnings("unchecked")
    public static List<com.ecm.km.entity.TopicCategory> mapListToTopicCategoryList(Object mapList) {
        if (mapList == null) {
            return new ArrayList<>();
        }
        
        List<com.ecm.km.entity.TopicCategory> result = new ArrayList<>();
        
        if (mapList instanceof List) {
            List<?> list = (List<?>) mapList;
            for (Object item : list) {
                if (item instanceof Map) {
                    Map<String, Object> map = (Map<String, Object>) item;
                    com.ecm.km.entity.TopicCategory topicCategory = mapToTopicCategory(map);
                    if (topicCategory != null) {
                        result.add(topicCategory);
                    }
                }
            }
        }
        
        return result;
    }
} 