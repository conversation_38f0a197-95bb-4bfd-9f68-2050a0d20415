package com.ecm.flowable.service;

import com.ecm.core.cache.manager.CacheManagerOper;
import com.ecm.core.cache.manager.SessionManager;
import com.ecm.core.entity.EcmContent;
import com.ecm.core.entity.EcmDocument;
import com.ecm.core.entity.EcmParameter;
import com.ecm.core.entity.EcmRelation;
import com.ecm.core.exception.AccessDeniedException;
import com.ecm.core.exception.EcmException;
import com.ecm.core.exception.NoPermissionException;
import com.ecm.core.service.AuthService;
import com.ecm.core.service.ContentService;
import com.ecm.core.service.DocumentService;
import com.ecm.core.service.RelationService;
import com.ecm.flowable.listener.BaseListener;
import com.ecm.icore.service.IEcmSession;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

@Service
@Transactional(rollbackFor = Exception.class,propagation = Propagation.REQUIRES_NEW)
public class WorkflowDataService {
    protected static IEcmSession  ecmSession = null;
    @Autowired
    protected AuthService authService;
    @Autowired
    public ContentService contentService;
    @Autowired
    public DocumentService documentService;
    @Autowired
    protected Environment env;

    @Autowired
    private RelationService relationService;

    public boolean updateContent(String token,EcmContent en) throws EcmException {
        return contentService.updateObject(token,en);
    }
    @Transactional(rollbackFor = Exception.class)
    public void newRelation(String token, EcmRelation relation) throws EcmException {
        relationService.newObject(token,relation);
    }

    public EcmContent getPrimaryContent(String token, String docId){
        return contentService.getPrimaryContent(token,docId);
    }

    public EcmContent getContent(String token, String docId,int contentType,String formatName){
        return contentService.getObject(token,docId,contentType,formatName);
    }

    public EcmContent getContentByContentId(String token, String id) throws AccessDeniedException, EcmException, NoPermissionException {
        return contentService.getObjectById(token,id);
    }

    public InputStream getContentStream(String token, EcmContent content, String docId) throws AccessDeniedException, NoPermissionException, EcmException {
        return documentService.getContentStream(token,content,docId);
    }

    @SneakyThrows
    public InputStream getContentStream(String token, EcmContent content) {
        return contentService.getContentStream(token,content);
    }

    public EcmDocument getObjectById(String id) throws EcmException {
        EcmDocument formObj= documentService.getObjectById(getSession().getToken(),id);
        return formObj;
    }

    public void updateObject(String token, EcmDocument doc, EcmContent content, boolean needValidatePolicy)
            throws NoPermissionException, AccessDeniedException, EcmException {

        documentService.updateObject2(token,doc,content,needValidatePolicy);
    }
    public void updateObject(String token, Map<String, Object> attrs,boolean needValidatePolicy) throws AccessDeniedException, NoPermissionException, EcmException {
        documentService.updateObject(token,attrs,needValidatePolicy);
    }

    public String createOrUpdateObject(String token,EcmDocument doc,EcmContent content,boolean needValidatePolicy) throws Exception {
        return documentService.creatOrUpdateObject(getSession().getToken(),doc,content,needValidatePolicy);
    }

    public void newObject(String token, EcmDocument doc, EcmContent content, boolean needValidatePolicy)
            throws Exception {
        documentService.newObject(token,doc,content,needValidatePolicy);
    }
    public Object getFormAttribute(String formId, String attrName) throws EcmException {
        EcmDocument formObj= documentService.getObjectById(getSession().getToken(),formId);
        return formObj.getAttributeValue(attrName);
    }
    public List<Map<String, Object>> getMapList(String token, String sql) throws EcmException {
        // TODO Auto-generated method stub
        return documentService.getMapList(token,sql);
    }
    public EcmDocument getOneDocInForm(String formId,String relationName) throws EcmException{
        if(relationName==null){
            relationName="irel_child";
        }
        String sql="select a.* from ecm_document a,ecm_relation b "
                + "where a.id=b.child_id and b.name='"+relationName+"' and b.parent_id='"+formId+"'";
        List<Map<String,Object>> resultData= documentService.getMapList(getSession().getToken(), sql);
        if(resultData!=null&&resultData.size()>0) {
            EcmDocument docu=new EcmDocument();
            docu.setAttributes(resultData.get(0));
            return docu;
        }
        return null;
    }
    public void queue(String token, String id, String name, String eventName, String message)
    {
        try {
            documentService.queue(token,id,name,
                    eventName,message);
        } catch (EcmException e) {
            e.printStackTrace();
        } catch (AccessDeniedException e) {
            e.printStackTrace();
        }
    }
    /**
     * 获取session
     * @return
     */
    protected IEcmSession getSession(){             //统一获取Session

        try {
            String workflowUserName="wfadmin";
            String password="1IlewAZnjIftb81eHk5TAcRljDwXa4RG";

            EcmParameter userNameParam= CacheManagerOper.getEcmParameter("workflowUserName");
            EcmParameter passwordParam= CacheManagerOper.getEcmParameter("workflowUserPassword");
            if(userNameParam!=null) {
                workflowUserName=userNameParam.getValue();
            }else {
                workflowUserName=env.getProperty("ecm.username");
            }

            if(passwordParam!=null) {
                password=passwordParam.getValue();
            }else {
                password=env.getProperty("ecm.password");
            }
            if(ecmSession==null){
                ecmSession = authService.login("workflow",workflowUserName, password);
            }else if(ecmSession!=null){
                IEcmSession session = SessionManager.getInstance().getSession(ecmSession.getToken());
                if(session==null){
                    ecmSession = authService.login("workflow",workflowUserName, password);
                }
            }

        }
        catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return ecmSession;
    }

    public void grantGroup(String token, String id, String targetName, int permission, Date expireDate, boolean newAcl) throws AccessDeniedException, EcmException, NoPermissionException {
        documentService.grantGroup(token,id,targetName,permission,expireDate,newAcl);
    }

    public void grantUser(String token, String id, String targetName, int permission, Date expireDate, boolean newAcl) throws AccessDeniedException, EcmException, NoPermissionException {
        documentService.grantUser(token,id,targetName,permission,expireDate,newAcl);
    }

    public List<EcmContent> getAllRendition(String token, String docId) throws AccessDeniedException, NoPermissionException, EcmException {
        return documentService.getAllRendition(token,docId);
    }
}
